/*! normalize.css v7.0.0 | MIT License | github.com/necolas/normalize.css */

/* Document
   ========================================================================== */

/**
 * 1. Correct the line height in all browsers.
 * 2. Prevent adjustments of font size after orientation changes in
 *    IE on Windows Phone and in iOS.
 */

html {
  line-height: 1.15; /* 1 */
  -ms-text-size-adjust: 100%; /* 2 */
  -webkit-text-size-adjust: 100%; /* 2 */
}

/* Sections
   ========================================================================== */

/**
 * Remove the margin in all browsers (opinionated).
 */

body {
  margin: 0;
}

/**
 * Add the correct display in IE 9-.
 */

article,
aside,
footer,
header,
nav,
section {
  display: block;
}

/**
 * Correct the font size and margin on `h1` elements within `section` and
 * `article` contexts in Chrome, Firefox, and Safari.
 */

h1 {
  font-size: 2em;
  margin: 0.67em 0;
}

/* Grouping content
   ========================================================================== */

/**
 * Add the correct display in IE 9-.
 * 1. Add the correct display in IE.
 */

figcaption,
figure,
main { /* 1 */
  display: block;
}

/**
 * Add the correct margin in IE 8.
 */

figure {
  margin: 1em 40px;
}

/**
 * 1. Add the correct box sizing in Firefox.
 * 2. Show the overflow in Edge and IE.
 */

hr {
  box-sizing: content-box; /* 1 */
  height: 0; /* 1 */
  overflow: visible; /* 2 */
}

/**
 * 1. Correct the inheritance and scaling of font size in all browsers.
 * 2. Correct the odd `em` font sizing in all browsers.
 */

pre {
  font-family: monospace, monospace; /* 1 */
  font-size: 1em; /* 2 */
}

/* Text-level semantics
   ========================================================================== */

/**
 * 1. Remove the gray background on active links in IE 10.
 * 2. Remove gaps in links underline in iOS 8+ and Safari 8+.
 */

a {
  background-color: transparent; /* 1 */
  -webkit-text-decoration-skip: objects; /* 2 */
}

/**
 * 1. Remove the bottom border in Chrome 57- and Firefox 39-.
 * 2. Add the correct text decoration in Chrome, Edge, IE, Opera, and Safari.
 */

abbr[title] {
  border-bottom: none; /* 1 */
  text-decoration: underline; /* 2 */
  text-decoration: underline dotted; /* 2 */
}

/**
 * Prevent the duplicate application of `bolder` by the next rule in Safari 6.
 */

b,
strong {
  font-weight: inherit;
}

/**
 * Add the correct font weight in Chrome, Edge, and Safari.
 */

b,
strong {
  font-weight: bolder;
}

/**
 * 1. Correct the inheritance and scaling of font size in all browsers.
 * 2. Correct the odd `em` font sizing in all browsers.
 */

code,
kbd,
samp {
  font-family: monospace, monospace; /* 1 */
  font-size: 1em; /* 2 */
}

/**
 * Add the correct font style in Android 4.3-.
 */

dfn {
  font-style: italic;
}

/**
 * Add the correct background and color in IE 9-.
 */

mark {
  background-color: #ff0;
  color: #000;
}

/**
 * Add the correct font size in all browsers.
 */

small {
  font-size: 80%;
}

/**
 * Prevent `sub` and `sup` elements from affecting the line height in
 * all browsers.
 */

sub,
sup {
  font-size: 75%;
  line-height: 0;
  position: relative;
  vertical-align: baseline;
}

sub {
  bottom: -0.25em;
}

sup {
  top: -0.5em;
}

/* Embedded content
   ========================================================================== */

/**
 * Add the correct display in IE 9-.
 */

audio,
video {
  display: inline-block;
}

/**
 * Add the correct display in iOS 4-7.
 */

audio:not([controls]) {
  display: none;
  height: 0;
}

/**
 * Remove the border on images inside links in IE 10-.
 */

img {
  border-style: none;
}

/**
 * Hide the overflow in IE.
 */

svg:not(:root) {
  overflow: hidden;
}

/* Forms
   ========================================================================== */

/**
 * 1. Change the font styles in all browsers (opinionated).
 * 2. Remove the margin in Firefox and Safari.
 */

button,
input,
optgroup,
select,
textarea {
  font-family: sans-serif; /* 1 */
  font-size: 100%; /* 1 */
  line-height: 1.15; /* 1 */
  margin: 0; /* 2 */
}

/**
 * Show the overflow in IE.
 * 1. Show the overflow in Edge.
 */

button,
input { /* 1 */
  overflow: visible;
}

/**
 * Remove the inheritance of text transform in Edge, Firefox, and IE.
 * 1. Remove the inheritance of text transform in Firefox.
 */

button,
select { /* 1 */
  text-transform: none;
}

/**
 * 1. Prevent a WebKit bug where (2) destroys native `audio` and `video`
 *    controls in Android 4.
 * 2. Correct the inability to style clickable types in iOS and Safari.
 */

button,
html [type="button"], /* 1 */
[type="reset"],
[type="submit"] {
  -webkit-appearance: button; /* 2 */
}

/**
 * Remove the inner border and padding in Firefox.
 */

button::-moz-focus-inner,
[type="button"]::-moz-focus-inner,
[type="reset"]::-moz-focus-inner,
[type="submit"]::-moz-focus-inner {
  border-style: none;
  padding: 0;
}

/**
 * Restore the focus styles unset by the previous rule.
 */

button:-moz-focusring,
[type="button"]:-moz-focusring,
[type="reset"]:-moz-focusring,
[type="submit"]:-moz-focusring {
  outline: 1px dotted ButtonText;
}

/**
 * Correct the padding in Firefox.
 */

fieldset {
  padding: 0.35em 0.75em 0.625em;
}

/**
 * 1. Correct the text wrapping in Edge and IE.
 * 2. Correct the color inheritance from `fieldset` elements in IE.
 * 3. Remove the padding so developers are not caught out when they zero out
 *    `fieldset` elements in all browsers.
 */

legend {
  box-sizing: border-box; /* 1 */
  color: inherit; /* 2 */
  display: table; /* 1 */
  max-width: 100%; /* 1 */
  padding: 0; /* 3 */
  white-space: normal; /* 1 */
}

/**
 * 1. Add the correct display in IE 9-.
 * 2. Add the correct vertical alignment in Chrome, Firefox, and Opera.
 */

progress {
  display: inline-block; /* 1 */
  vertical-align: baseline; /* 2 */
}

/**
 * Remove the default vertical scrollbar in IE.
 */

textarea {
  overflow: auto;
}

/**
 * 1. Add the correct box sizing in IE 10-.
 * 2. Remove the padding in IE 10-.
 */

[type="checkbox"],
[type="radio"] {
  box-sizing: border-box; /* 1 */
  padding: 0; /* 2 */
}

/**
 * Correct the cursor style of increment and decrement buttons in Chrome.
 */

[type="number"]::-webkit-inner-spin-button,
[type="number"]::-webkit-outer-spin-button {
  height: auto;
}

/**
 * 1. Correct the odd appearance in Chrome and Safari.
 * 2. Correct the outline style in Safari.
 */

[type="search"] {
  -webkit-appearance: textfield; /* 1 */
  outline-offset: -2px; /* 2 */
}

/**
 * Remove the inner padding and cancel buttons in Chrome and Safari on macOS.
 */

[type="search"]::-webkit-search-cancel-button,
[type="search"]::-webkit-search-decoration {
  -webkit-appearance: none;
}

/**
 * 1. Correct the inability to style clickable types in iOS and Safari.
 * 2. Change font properties to `inherit` in Safari.
 */

::-webkit-file-upload-button {
  -webkit-appearance: button; /* 1 */
  font: inherit; /* 2 */
}

/* Interactive
   ========================================================================== */

/*
 * Add the correct display in IE 9-.
 * 1. Add the correct display in Edge, IE, and Firefox.
 */

details, /* 1 */
menu {
  display: block;
}

/*
 * Add the correct display in all browsers.
 */

summary {
  display: list-item;
}

/* Scripting
   ========================================================================== */

/**
 * Add the correct display in IE 9-.
 */

canvas {
  display: inline-block;
}

/**
 * Add the correct display in IE.
 */

template {
  display: none;
}

/* Hidden
   ========================================================================== */

/**
 * Add the correct display in IE 10-.
 */

[hidden] {
  display: none;
}



/* Padding Top */
.pt-5{padding-top:5px;}
.pt-10{padding-top:10px;}
.pt-15{padding-top:15px;}
.pt-20{padding-top:20px;}
.pt-25{padding-top:25px;}
.pt-30{padding-top:30px;}
.pt-35{padding-top:35px;}
.pt-40{padding-top:40px;}
.pt-45{padding-top:45px;}
.pt-50{padding-top:50px;}
.pt-55{padding-top:55px;}
.pt-60{padding-top:60px;}
.pt-65{padding-top:65px;}
.pt-70{padding-top:70px;}
.pt-75{padding-top:75px;}
.pt-80{padding-top:80px;}
.pt-85{padding-top:85px;}
.pt-90{padding-top:90px;}
.pt-95{padding-top:95px;}
.pt-100{padding-top:100px;}
.pt-105{padding-top:105px;}
.pt-110{padding-top:110px;}
.pt-120{padding-top:120px;}
.pt-130{padding-top:130px;}
.pt-140{padding-top:140px;}
.pt-150{padding-top:150px;}
.pt-160{padding-top:160px;}
.pt-170{padding-top:170px;}
.pt-180{padding-top:180px;}
.pt-190{padding-top:190px;}
.pt-200{padding-top:200px;}


/* Padding Bottom */
.pb-5{padding-bottom:5px;}
.pb-10{padding-bottom:10px;}
.pb-15{padding-bottom:15px;}
.pb-20{padding-bottom:20px;}
.pb-25{padding-bottom:25px;}
.pb-30{padding-bottom:30px;}
.pb-35{padding-bottom:35px;}
.pb-40{padding-bottom:40px;}
.pb-45{padding-bottom:45px;}
.pb-50{padding-bottom:50px;}
.pb-55{padding-bottom:55px;}
.pb-60{padding-bottom:60px;}
.pb-65{padding-bottom:65px;}
.pb-70{padding-bottom:70px;}
.pb-75{padding-bottom:75px;}
.pb-80{padding-bottom:80px;}
.pb-85{padding-bottom:85px;}
.pb-90{padding-bottom:90px;}
.pb-95{padding-bottom:95px;}
.pb-100{padding-bottom:100px;}
.pb-110{padding-bottom:110px;}
.pb-120{padding-bottom:120px;}
.pb-130{padding-bottom:130px;}
.pb-140{padding-bottom:140px;}
.pb-150{padding-bottom:150px;}
.pb-160{padding-bottom:160px;}
.pb-170{padding-bottom:170px;}
.pb-180{padding-bottom:180px;}
.pb-190{padding-bottom:190px;}
.pb-200{padding-bottom:200px;}


/* margin Top */
.mt-5{margin-top:5px;}
.mt-10{margin-top:10px;}
.mt-15{margin-top:15px;}
.mt-20{margin-top:20px;}
.mt-25{margin-top:25px;}
.mt-30{margin-top:30px;}
.mt-35{margin-top:35px;}
.mt-40{margin-top:40px;}
.mt-45{margin-top:45px;}
.mt-50{margin-top:50px;}
.mt-55{margin-top:55px;}
.mt-60{margin-top:60px;}
.mt-65{margin-top:65px;}
.mt-70{margin-top:70px;}
.mt-75{margin-top:75px;}
.mt-80{margin-top:80px;}
.mt-85{margin-top:85px;}
.mt-90{margin-top:90px;}
.mt-95{margin-top:95px;}
.mt-100{margin-top:100px;}
.mt-110{margin-top:110px;}
.mt-120{margin-top:120px;}
.mt-130{margin-top:130px;}
.mt-140{margin-top:140px;}
.mt-150{margin-top:150px;}
.mt-160{margin-top:160px;}
.mt-170{margin-top:170px;}
.mt-180{margin-top:180px;}
.mt-190{margin-top:190px;}
.mt-200{margin-top:200px;}


/* margin bottom */
.mb-5{margin-bottom:5px;}
.mb-10{margin-bottom:10px;}
.mb-15{margin-bottom:15px;}
.mb-20{margin-bottom:20px;}
.mb-25{margin-bottom:25px;}
.mb-30{margin-bottom:30px;}
.mb-35{margin-bottom:35px;}
.mb-40{margin-bottom:40px;}
.mb-45{margin-bottom:45px;}
.mb-50{margin-bottom:50px;}
.mb-55{margin-bottom:55px;}
.mb-60{margin-bottom:60px;}
.mb-65{margin-bottom:65px;}
.mb-70{margin-bottom:70px;}
.mb-75{margin-bottom:75px;}
.mb-80{margin-bottom:80px;}
.mb-85{margin-bottom:85px;}
.mb-90{margin-bottom:90px;}
.mb-95{margin-bottom:95px;}
.mb-100{margin-bottom:100px;}
.mb-110{margin-bottom:110px;}
.mb-120{margin-bottom:120px;}
.mb-130{margin-bottom:130px;}
.mb-140{margin-bottom:140px;}
.mb-150{margin-bottom:150px;}
.mb-160{margin-bottom:160px;}
.mb-170{margin-bottom:170px;}
.mb-180{margin-bottom:180px;}
.mb-190{margin-bottom:190px;}
.mb-200{margin-bottom:200px;}