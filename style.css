/*
Template Name:Zomo IT Solutions Bootstrap4 HTML5 Template
Description: Zomo IT Solutions Bootstrap4 HTML5 Template
Version: 1.0
*/

body{
	margin: 0;
	padding: 0;
	font-size: 16px;
	color: #191919;
	font-family: 'Catamaran', sans-serif;
	font-weight: normal;
	font-style: normal;
}
@import url('https://fonts.googleapis.com/css2?family=Catamaran:wght@400;500;600;700&family=Open+Sans:wght@400;500;600;700&display=swap');
a,
button {
	-webkit-transition: all 0.3s ease-out 0s;
	-moz-transition: all 0.3s ease-out 0s;
	-ms-transition: all 0.3s ease-out 0s;
	-o-transition: all 0.3s ease-out 0s;
	transition: all 0.3s ease-out 0s;
}
*::-moz-selection {
	background: #d6b161;
	color: #fff;
	text-shadow: none;
}
::-moz-selection {
	background: #444;
	color: #fff;
	text-shadow: none;
}
::selection {
	background: #444;
	color: #fff;
	text-shadow: none;
}
*::-moz-placeholder {
	color: #555555;
	font-size: 14px;
	opacity: 1;
}
*::placeholder {
	color: #555555;
	font-size: 14px;
	opacity: 1;
}
h1,h2,h3,h4,h5,h6,p{
	margin:0;
	padding: 0;
}
ul{
	margin:0;
	padding: 0;
	list-style: none;
}
a{
	text-decoration: none;
	transition: .4s;
	-webkit-transition: all .4s ease-in-out;
}
a:hover{
	text-decoration: none;
	color: #82B60B;
}
button:focus{
	outline: none;
}
input:focus{
	outline: none;
}
p{
	color: #293a5c;
	font-weight: 400;
	font-family: 'Open Sans', sans-serif;
	font-size: 17px;
	line-height: 28px;
	word-spacing: 2px;
}
/*Scroll Area*/
.scroll-area {
    position: fixed;
    bottom: 20px;
    right: 20px;
    z-index:999;
    display: none;
}
.scroll-area i {
    width: 35px;
    height: 40px;
    background-color: #2D3580;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    color: #fff;
    font-size: 20px;
}
.scroll-area i:hover {
    background: #000;
}
.section-padding{
	padding: 80px 0px;
}
/*Header area*/
/* loader */
#preloader {
  position: fixed;
  top:0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #fff;
  z-index: 999999; 
  display: block;

}
.loader{
    width: 100px;
    height: 100px;
    margin: 50px auto;
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 200px;
}
.loader .loader-inner-1,
.loader .loader-inner-2,
.loader .loader-inner-3,
.loader .loader-inner-4{
    display: block;
    width: 20px;
    height: 20px;
    border-radius: 20px;
    position: absolute;
}
.loader .loader-inner-1:before,
.loader .loader-inner-2:before,
.loader .loader-inner-3:before,
.loader .loader-inner-4:before{
    content: "";
    display: block;
    width: 20px;
    height: 20px;
    border-radius: 20px;
    position: absolute;
    right: 0;
    animation-name: loading-1;
    animation-iteration-count: infinite;
    animation-direction: normal;
    animation-duration: 2s;
}
.loader .loader-inner-1{
    top: 0;
    left: 0;
    transform: rotate(70deg);
}
.loader .loader-inner-1:before{ background: #06aed5; }
.loader .loader-inner-2{
    top: 0;
    right: 0;
    transform: rotate(160deg);
}
.loader .loader-inner-2:before{ background: #ec008c; }
.loader .loader-inner-3{
    bottom: 0;
    right: 0;
    transform: rotate(-110deg);
}
.loader .loader-inner-3:before{ background: #ffbf00; }
.loader .loader-inner-4{
    bottom: 0;
    left: 0;
    transform: rotate(-20deg);
}
.loader .loader-inner-4:before{ background: #079c00; }
@keyframes loading-1{
    0%{
        width: 20px;
        right: 0;
    }
    30%{
        width: 120px;
        right: -100px;
    }
    60%{
        width: 20px;
        right: -100px;
    }
}


.logo a img {
	width: 90px;
	height: auto;
}
.header-area{
	position: relative;
	background: #fff;
	padding: 22px 0px;
	overflow: hidden;
}

.header-content {
	
}
.header-nav {
	position: relative;
	display: flex;
	justify-content: flex-end;
}
.header-content ul li{
	font-size: 16px;
	font-weight: 500;
	margin-right: 10px;
	font-family: 'Catamaran', sans-serif;
}
.header-content ul li a {
	color: #051242;
	letter-spacing: 2px;
}
.header-content ul li a:hover{
	color: #535a62;
}
.header-content ul li i {
	font-size: 16px;
	margin-right: 6px;
	color: #163666;
}
.header-nav::before {
	position: absolute;
	content: '';
	width: 2px;
	height: 23px;
	background: #1A1695;
	right: -29px;
	top: 3px;
}
.header-content-right ul{
	display: flex;
	justify-content: center;
}
.header-content-right ul li a{
	padding-left: 12px;
	font-size: 16px;
	color: #163666;
	transition: all .3s ease;
}
.header-content-right ul li a:hover i{
	transform: translateY(-2px);
	transition: all .3s ease;
}
/*Header area*/
/*navbar*/
/*.header .nav-menu{
	padding:0 15px;
}*/

.header-main {
	display: flex;
	justify-content: flex-start;
}
.header .menu > .menu-iteam{
	display: inline-block;
	margin-left:18px;
	position: relative;
}

.header .menu > .menu-iteam a {
	display: block;
	padding: 12px 0;
	color: #fff;
	font-size: 14px;
	text-transform: uppercase;
	font-weight: 600;
	transition: all 0.3s ease;
	text-decoration: none;
	font-family: 'Catamaran', sans-serif;
}
.header .menu > .menu-iteam a .plus{
	display: inline-block;
	height: 12px;
	width: 12px;
	position: relative;
	margin-left: 5px;
	pointer-events: none;
}
.header .menu > .menu-iteam a .plus:before,
.header .menu > .menu-iteam a .plus:after{
	content: '';
	position: absolute;
	box-sizing: border-box;
	top: 50%;
	left: 50%;
	background:#fff;
	height: 2px;
	width: 100%;
	transform: translate(-50%,-50%);
	transition: all 0.3s ease;
}
.header .menu > .menu-iteam:hover a .plus:before,
.header .menu > .menu-iteam:hover a .plus:after{
	background: #3EBDF2;
}

.header .menu > .menu-iteam a .plus:after{
	transform: translate(-50%,-50%) rotate(-90deg);
}
.header .menu > .menu-iteam > .submenu > .menu-iteam > a:hover{
	color:#7DAFDF;
}
.header .menu > .menu-iteam:hover> a {
	color:#7DAFDF;
}
.header .menu > .menu-iteam > .submenu {
	box-shadow: 0 0 10px rgba(0,0,0,0.2);
	position: absolute;
	left: 0;
	width: 220px;
	background: white;
	top: 100%;
	padding: 10px 0;
	border-top: 3px solid #83A9F2;
	transform: translateY(10px);
	opacity: 0;
	visibility: hidden;
	z-index: 1000;
}
@media(min-width: 992px){
	.header .menu > .menu-iteam-has-childrean:hover >.submenu{
	transform: translateY(0px);
	transition: all 0.3s ease;
	opacity: 1;
	visibility: visible;
}
.header .menu > .menu-iteam-has-childrean:hover > a .plus::after{
	transform: translate(-50%,-50%) rotate(0deg);
}
}
.header .menu > .menu-iteam >.submenu >.menu-iteam{
	display: block;
}

.header .menu > .menu-iteam > .submenu >.menu-iteam > a{
	display: block;
	padding:10px 20px;
	font-size: 14px;
	color:#000000;
	font-weight: 600;
	font-family: 'Catamaran', sans-serif;
	transition: all 0.3s ease;
	text-transform: capitalize;
	text-decoration: none;
}
.header .open-nav-menu{
	height: 34px;
	width: 40px;
	align-items: center;
	justify-content: end;
	cursor: pointer;
	display:none;
}

.header .open-nav-menu span{
	display: block;
	height: 3px;
	width: 24px;
	background-color:#fff;
	position: relative;
}
.header .open-nav-menu span::before,
.header .open-nav-menu span::after{
	content: ''; 
	position: absolute;
	left:0;
	width: 100%;
	height: 100%;
	background-color: #fff;
	box-sizing: border-box;
}
.header .open-nav-menu span::before{
	top: -7px;
}
.header .open-nav-menu span::after{
	top:7px;
}
.header .close-nav-menu{
	height: 40px;
	width: 40px;
	background-color: white;
	margin:0 0 15px 15px;
	cursor: pointer;
	align-items: center;
	justify-content: center;
	display: none;
}
.header .close-nav-menu img{
	width: 18px;
}


.header .menu-overly{
	position: fixed;
	z-index: 999;
	background-color: rgba(0,0,0,0.5);
	left: 0;
	top: 0;
	height: 100%;
	width: 100%;
	visibility: hidden;
	opacity: 0;
	transition: all 0.3s ease;
}
.header .menu-overly.active{
	visibility: visible;
	opacity: 1; 
}

/*navbar*/

/*nav-area*/
.navbar-area{
	position: relative;
}
.full-nav {
	position: absolute;
	top: 4px;
	left: 0;
	padding-top: 15px;
	padding-bottom: 15px;
	width: 100%;
	z-index: 999;
	height: auto;
}
.nav-right {
	
	display: flex;
	justify-content: space-evenly;
}
.nav-right form{
	position: relative;
}
.nav-right .form-control {
	display: block;
	width: 100%;
	padding: .375rem .75rem;
	font-size: 1rem;
	font-weight: 400;
	line-height: 1.5;
	color: #fff;
	background-color:transparent;
	background-clip: padding-box;
	border: none;
	border-bottom: 1px solid white;
	appearance: none;
	border-radius: 0px;
	opacity: 0.7;
}
.nav-right .form-control::placeholder {
	color: #fff;
	opacity: 1;
	font-size: 16px;
	font-weight: 400;
}
 .form-control:focus{
	outline: none;
	box-shadow: none;
	border-bottom: 1px solid #6488bd;
}
.form-control:focus {
	color: #212529;
	background-color: #fff;
	border-color: #6488bd;
	outline: 0;
	box-shadow: 0 0 0 1px #6488bd;
}
.navbar-area .nav-right button {
	background: none;
	border: none;
	color: #fff;
	font-size: 20px;
	position: absolute;
	top: 7px;
	right: 0;
	cursor: pointer;
	-webkit-transition: all .5s;
	transition: all .5s;
	z-index: 999;
}


.nav-btn {
	position: relative;
	margin-right: 0px;
	margin-top: 3px;
}

 .nav-btn .box-btn {
	font-size: 16px;
	color: #fff;
	padding: 15px 25px;
	line-height: 1;
	-webkit-transition: all .5s;
	transition: all .5s;
	text-transform: capitalize;
	position: relative;
	cursor: pointer;
	border-radius: 4px;
	text-align: center;
	z-index: 1;
	background-color: #245480;
	border: 1px solid #2D84A8;
	font-family: 'Open Sans', sans-serif;
}
.nav-btn-2{
	margin-left: 20px;
}
.box-btn:hover{
	color: #000;
}
.nav-btn .box-btn::after {
	content: '';
	position: absolute;
	bottom: 0;
	right: 0;
	width: 0;
	height: 50%;
	background: #fff;
	z-index: -1;
	transition: all .4s ease;
}
.nav-btn .box-btn::before {
	content: "";
	position: absolute;
	top: 0;
	left: 0;
	width: 0;
	height: 50%;
	background-color: #fff;
	z-index: -1;
	-webkit-transition: all .5s;
	transition: all .5s;
}
 .box-btn::after, .box-btn::before {
	background: #fff;
}

.box-btn:hover::before{
	left: 0;
	width: 100%;
}
.box-btn:hover::after{
	right: 0;
	width: 100%;
}
/*nav-area*/

/*banner-area*/
.banner-area{
	background: url('assets/img/home1/banner.png')no-repeat scroll 0 0 / 100% 100%;
	width: 100%;
	position: relative;
	z-index:1;
	width: 100%;
	height:700px;
}
.banner-area::before {
	width: 100%;
	height: 100%;
	content: '';
	position: absolute;
	background: #0F2653;
	opacity: 0.6;
	z-index:-1;
	left: 0;
	top: 0;
}
.banner-content {
	margin-top: 30%;
	color: white;
	margin-left: auto;
	max-width: 550px;
	z-index: 1;
}
.banner-content h2 {
	color: #fff;
	font-weight: 600;
	font-size: 40px;
}
.banner-content p {
	color: #fff;
	font-weight: 400;
	font-family: 'Open Sans', sans-serif;
	font-size: 17px;
	line-height: 29px;
	word-spacing: 4px;
	padding: 28px 0px;
}
.banner-btn {
	display: flex;
	padding-top: 15px;
}
.banner-img {
	margin-top:60px;
 -webkit-animation: pulse 7s infinite;
 animation: pulse 7s infinite;
  float: left;
}

@keyframes pulse {
0% {
   
    transform: scaleX(1);
}
50% {
    transform: scale3d(1.04,1.04,1.04);
}
100% {
    
    transform: scaleX(1);
}

}
.banner-img img{
	max-width: 100%;
	height: auto;
}

.home-shape .shape1 {
	position: absolute;
	bottom: 20%;
	left: 40%;
	-webkit-animation: roteted 14s linear infinite;
	animation: shape1animtion 14s linear infinite;
}
.home-shape .shape2 {
	position: absolute;
	top: 20%;
	left: 4%;
	-webkit-animation: rotate360 6s linear infinite;
	animation: rotate360 6s linear infinite;
}

.home-shape .shape3 {
	position: absolute;
	top: 10%;
	right: 20%;
	-webkit-animation: roteted 14s linear infinite;
	animation: shape1animtion 14s linear infinite;
}

.home-shape .shape4 {
	position: absolute;
	bottom: 12%;
	left: 8%;
	-webkit-animation: rotate360 6s linear infinite;
	animation: rotate360 6s linear infinite;
}
.home-shape .shape5 {
	position: absolute;
	top: 35%;
	left: 2%;
	-webkit-animation: roteted 14s linear infinite;
	animation: shape1animtion 14s linear infinite;
}
@keyframes rotate360{
	0%{
		transform: rotate(0deg);
	}
	100%{
		transform: rotate(-1turn);
	}
}
@keyframes shape1animtion{
	0%{
		transform: translate(0) rotate(0deg);
	}
	20%{
		transform: translate(73px,-1px) rotate(36deg);
	}
	40%{
		transform: translate(141px,72px) rotate(72deg);
	}
	60%{
		transform: translate(83px,122px) rotate(108deg);
	}
	80%{
		transform: translate(-40px,72px) rotate(144deg);
	}
	100%{
		transform: translate(0) rotate(0deg);
	}
}
/*banner-area*/

/*.section-title*/
.section-title{
	text-align: center;
	position: relative;
}
.section-title h2 {
	font-size: 40px;
	font-weight: 600;
	text-transform: capitalize;
	color: #293a5c;
	padding-bottom: 20px;
	width: 66%;
	margin: 0 auto;
}


.section-title p {
	color: #293a5c;
	font-weight: 400;
	font-family: 'Open Sans', sans-serif;
	font-size: 17px;
	line-height: 29px;
	word-spacing: 4px;
	width: 80%;
	text-align: center;
	margin: 0 auto;
}
/*.section-area*/
.service-area{
	overflow: hidden;
}

.service-img img{
	width: 60px;
	height: auto;
}

.service-single {
	position: relative;
	text-align: center;
	padding: 30px;
	margin-bottom: 30px;
	transition: all .5s ease;
	border-radius: 4px;
	z-index: 1;
	overflow: hidden;
	box-shadow: 0 3px 17px 2px #e3dede;
	cursor: pointer;
}
.service-single::after{
	position: absolute;
	content: '';
	top:0;
	width: 100%;
	height: 100%;
	right: 100%;
	z-index: -1;
	background: url('assets/img/service/service-hover.png');
	transition: all .5s ease;
}

.service-single::before {
	position: absolute;
	content: '';
	top: 0;
	width: 100%;
	height: 100%;
	left: 100%;
	z-index: -1;
	background: #245480;
	transition: all .5s ease;
}
.service-single:hover::after{
	right: 0;
}

.service-single:hover::before{
	left: 0;
}

.service-single:hover{
	transform: translateY(-10px);
}
.service-single .service-content h3 {
  color: #051242;
  margin-bottom: 5px;
}
.service-single .service-content p {
  margin-bottom: 10px;
}
.service-single:hover p {
  color: #e9e9e9;

}
.service-single .service-content:hover h3 {
  color: #fff;
}
.service-single:hover .service-content .service-bnt {
  color: #fff;
}
.service-bnt {
  font-size: 14px;
  color: #293a5c;
  font-weight: 600;
  line-height: 1;
}
.service-bnt:hover{
	letter-spacing: 2px;
} 

/*company-area*/
.company-area {
	background: #EAEDF4;
	overflow: hidden;
}
.company-title {
	margin-top: 50px;
}
.company-img img{
	width: 100%;
	height: auto;
}

.company-img {
	animation: updown 5s infinite;
}
.company-title span {
	color: #4E5BA2;
	font-size: 16px;
	font-weight: 600;
}
.company-title h2{
	font-size: 40px;
	font-weight: 600;
	text-transform: capitalize;
	color: #293a5c;
	padding-bottom: 20px;
	padding-top: 10px;
}
.company-title p{
	padding-bottom: 10px;
}
.btn-2nd{
	margin-left: 0px;
	padding-top: 20px;
}
/*company-area*/

/*..chose area*/
.choose-area {
	overflow: hidden;
}
.choose-content {
	margin-top: 16px;
}
 .accordion-item {
	margin-bottom: 15px;
}
.choose-content .accordion-button {
	position: relative;
	display: flex;
	align-items: center;
	width: 100%;
	padding: 15px 20px;
	font-weight: 600;
	font-size: 18px;
	color: #293A5C !important;
	background-color: transparent;
	border-radius: 2px;
	box-shadow: 0px 2px 4px #bbbdbf;
	border: none;
}
.show{
	display:block;
}

	.accordion-body {
	padding: 1rem 1.25rem;
	background: white;
	border: 1px solid white;
	box-shadow: 0px 2px 3px #e3e3e3;
}


.choose-img img{
	width: 100%;
}

/*working*/
.working-area {
	background: #245480;
	overflow: hidden;
	
}
.single-process{
	text-align: center;
	color: #fff;
}
.icon-img img {
	width: 64px;
	height: 64px;
	background: #2e3862;
	padding: 8px;
	border-radius: 12px;
	
}
.single-process .process-content h3{
	padding: 10px 0px;
	text-transform: capitalize;
	font-size: 25px;
}
.single-process .process-content p {
	font-size: 15px;
	line-height: 25px;
	color: #fff;
	width: 96%;
	margin: 0 auto;
}

/*looking area*/
.looking-single{
	position: relative;
	transition: all .5s ease;
	border-radius: 4px;
	z-index: 1;
	overflow: hidden;
	box-shadow: 0 3px 17px 2px #e3dede;
	cursor: pointer;
	margin-top: 20px;
}
.looking-single:after{
	position: absolute;
	content: '';
	right: 100%;
	width: 100%;
	height: 100%;
	top: 0;
	background: url('assets/img/service/service-hover.png');
	transition: all .5s ease;
	z-index: -1;
}
.looking-single::before {
	position: absolute;
	content: '';
	left: 100%;
	width: 100%;
	height: 100%;
	top: 0;
	background: #0a365e;
	transition: all .5s ease;
	z-index: -1;
}
.looking-single:hover:after{
	right: 0;
}
.looking-single:hover:before{
	left: 0;
}
.looking-single:hover .looking-content h3{
	color: #fff;
}
.looking-single:hover .looking-content p,
.looking-single:hover .looking-content a{
	color: #fff;
}
.looking-img img{
	width: 100%;
}
.looking-content {
	padding: 29px 17px;
	box-sizing: border-box;
}
.looking-content h3{
  color: #051242;
  margin-bottom: 5px;
  font-weight: 500;
  text-transform: capitalize;
}
.looking-content p{
	letter-spacing: 0px;
	line-height: 26px;
	margin-bottom: 10px;
}
.looking-content a{
	color: #051242;
	text-transform: capitalize;
}
.more-text {
	padding: 25px 0px;
}
.more-text h6{
text-align: center;
}
.more-text h6 a {
	padding-left: 5px;
	color: #1C5D80;
}
/*looking area*/
/*features-area*/
.features-area {
	background: #F8FBFD;
	overflow: hidden;
	padding-top: 80px;
	padding-bottom: 80px;
}
.Features-nav li{
	color: #293A5C;
	font-family: 'Open Sans', sans-serif;
	font-weight: 500;
	padding-top: 5px;
}
.Features-nav li i {
	background: #1F5A92;
	width: 26px;
	height: 26px;
	border-radius: 50%;
	text-align: center;
	align-items: center;
	line-height: 27px;
	color: white;
	font-size: 10px;
	margin-right: 6px;
}
/*team area*/
.team-box{
	position: relative;
	margin: 0 5px;
	text-align: center;
	overflow: hidden;
}
.team-box .img img{
	width:100%;
	height: auto;
	border-radius: 15px;
}
.team-box .img{
	width: 100%;
	height: 100%;
	background-position: 0 0!important;
	position: relative;
	transition:1s ;
}
.team-box:hover .img{
	background-position: 50% 0!important;
}
.team-box .overly {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	background-color: rgba(0, 0, 0, 0.1);
	border-radius: 12px;
}
.team-box .overly2 {
	position: absolute;
	top: 0;
	left: -150%;
	width: 120%;
	height: 100%;
	background-color: #022251;
	transform: skewX(-30deg);
	transition: .4s;
	opacity: 0.7;
}
.team-box:hover .overly2{
	left: -9%;
}
.team-box .text {
	position: absolute;
	top: -6%;
	left: 48%;
	transform: translate(-50%,50%);
	width: 80%;
	color: black;
	box-sizing: border-box;
	padding: 25px;
	z-index: 1000;
	text-shadow: 5px 5px 10px black;
}
.team-box .text span:nth-child(1){
	position: absolute;
	top: 0;
	left: -20px;
	width: 0;
	height: 1px;
	background-color: white;
	transform-origin: left;
	transition: .7s ease-in-out;
	transition-delay: .7s;
}
.team-box .text span:nth-child(2){
	position: absolute;
	top: -20px;
	left: 0;
	width: 1px;
	height: 0;
	background-color: white;
	transform-origin: top;
	transition: .7s ease-in-out;
	transition-delay: 1.1s;
}
.team-box .text span:nth-child(3){
	position: absolute;
	bottom  : 0;
	right: -20px;
	width: 0;
	height: 1px;
	background-color: white;
	transform-origin: right;
	transition: .7s ease-in-out;
	transition-delay: .7s;
}
.team-box .text span:nth-child(4){
	position: absolute;
	bottom  :-20px!important;
	right: 0;
	width: 1px;
	height: 0;
	background-color: white;
	transform-origin: left;
	transition: .7s ease-in-out;
	transition-delay: 1.3s;
}
.team-box:hover .text span:nth-child(1),
.team-box:hover .text span:nth-child(3){
	width: 115%;
}
.team-box:hover .text span:nth-child(2),
.team-box:hover .text span:nth-child(4){
	height: 120%;
}

.team-box .text h2{
	position: relative;
	color: #E2E5EC;
	font-size: 1.5rem;
	text-transform: capitalize;
	margin-bottom: 0;
	letter-spacing: 1px;
	opacity: 0;
	transition: .5s;
	transition-timing-function:linear ;
	transform:translateY(-10px);
}

.team-box:hover .text h2{
	transform: translateY(0);
	opacity: 1;
	transition-delay: 1.3s;
}

.team-box .text p{
	line-height:1.2 ;
	opacity: 0;
	transform: translateY(10px);
	transition: .5s;
	transition-timing-function: linear;
	padding-top: 5px;
	font-size: 15px;
	font-weight: 500;
}
.team-box:hover .text p{
	transform: translateY(0);
	opacity: 1;
	transition-delay: 1.4s;
	color: #E2E5EC;;
}
.team-box .team-social{
	line-height:1.2 ;
	opacity: 0;
	transform: translateY(10px);
	transition: .5s;
	transition-timing-function: linear;
}
.team-box:hover .team-social{
	transform: translateY(0);
	opacity: 1;
	transition-delay: 1.4s;
	color: #E2E5EC;;
}
.team-social{
	padding: 10px;
	display: flex;
	justify-content: center;
}
.team-social li a{
	width: 30px;
	height: 30px;
	background: #293A5C;
	border-radius: 50%;
	margin: 0px 5px;
	text-align: center;
	align-items: center;
	justify-content: center;
	line-height: 24px;
	display: flex;
}
.team-social li a:hover{
	background: #fff;
	
}
.team-social li a:hover i {
	color: #0F3269;
}
.team-social li a i{
	color: #fff;
	font-size: 14px;
}
.team-slider.owl-theme .owl-dots .owl-dot.active span, .owl-theme .owl-dots .owl-dot:hover span {
	background:#1C6993;
}
.team-slider.owl-theme .owl-dots .owl-dot span {
	width: 19px;
}
.team-slider.owl-theme .owl-nav.disabled + .owl-dots {
	margin-top: 18px;
}

/*client-area*/
.client-area {
	background: #E9F4F4;
}
.client-single {
	text-align: center;
	background: #fff;
	padding: 30px;
	position: relative;
	margin-top: 30px;
	border-radius: 12px;

}
.client-single:hover img{
	border: 5px solid #0F3269;
	transform: translateY(-2px);
	transition: all .3s ease;
}
.client-single img{
	width: auto !important;
	position: absolute;
	top: -20px;
	left: 0;
	right: 0;
	margin: auto;
	border-radius: 50%;
	border: 5px solid #176A97;
}
.client-single p{
	margin-top: 20px;
	margin-bottom: 15px;
}
.client-single h3 {
	font-family: 'Open Sans', sans-serif;
	color: #293A5C;
	font-weight: 500;
	padding-bottom: 8px;
}
.client-single span{
	color: #293A5C;
	font-weight: 400;
}
.client-slider .owl-dots {
	display: none!important;
}

.client-slider.owl-theme .owl-nav .owl-next, .client-slider.owl-theme .owl-nav .owl-prev {
	position: absolute;
	left: 0;
	top:0;
	background-color: #71C6F4;
}

.client-slider.owl-theme .owl-nav .owl-next {
	left: auto;
	right: 0;
}
.client-slider.owl-theme .owl-nav .owl-prev {
	left: auto;
	left: 0;
}
.client-slider.owl-theme .owl-nav .owl-next, .client-slider.owl-theme .owl-nav .owl-prev {
	position: absolute;
	right: 0;
	top: -30px;
	background-color: #0E4D6E;
}
.owl-carousel .owl-nav button.owl-next, .owl-carousel .owl-nav button.owl-prev, .owl-carousel button.owl-dot {
	display: inline-block;
	padding: 3px 8px !important;
	color:#ADC1E8;
}
/*client-area*/
/*blog-area*/
.blog-single{
	transition: all .4s ease;
}
.blog-img a img{
	width: 100%;
	height: auto;
}
.blog-content {
	padding: 22px 25px;
	background: #fff;
	box-sizing: border-box;
	box-shadow: 0px 1px 3px #bcd2c8;
}
.blog-single:hover{
	transform: translateY(-7px);
	transition: all .4s ease;
}
.blog-content ul{
	display: flex;
}
.blog-content ul li {
	color: #595858d9;
	font-weight: 300;
	font-size: 14px;
}
.blog-content ul li a {
	position: relative;
	margin-left: 20px;
	color: #155679;
}
.blog-content ul li a::before {
	position: absolute;
	content: '';
	width: 2px;
	height: 14px;
	background: #71C6F4;
	left: -10px;
	top: 1px;
}
.blog-content h3 {
	color: #051242;
	padding-top: 7px;
	padding-bottom: 5px;
	font-weight: 600;
	text-transform: capitalize;
}
.blog-content:hover h3 {
	color: #176088;
	transition: all .3s ease;
}
.blog-content p{
	margin-bottom: 5px;
}
.blog-content a {
	font-size: 14px;
	color: #293a5c;
	font-weight: 600;
	line-height: 1;
}

/*contact us*/
.contact-us{
	background: #E9F4F4;
}
.contact-img img{
	width: 100%;

}
.contact-img{
	animation: updown 5s linear infinite;
}

@keyframes updown{
	0%{
		transform: translateY(-10px);
	}
	50%{
		transform: translateY(10px);
	}
	100%{
		transform: translateY(-10px);
	}
}

.contact-form {
	padding: 30px;
	background: #fff;
	border-radius: 16px;
	box-shadow: 0 0 20px 3px #c1c1c1;
}

.contact-form .form-group {
	margin-bottom: 25px;
}
.contact-form .form-control {
	padding: 10px 20px;
}
/*footer area*/
.footer-area{
	background: url('assets/img/home1/home-bg-2.png')no-repeat scroll 0 0 / 100% 100%;
	overflow: hidden;
	padding-bottom: 20px;
	position: relative;
	z-index: 1;
}

.footer-area::before {
	width: 100%;
	height: 100%;
	position: absolute;
	content: '';
	background: #397697;
	opacity: 0.2;
	z-index: -1;
}

.footer-area .subcribe-form{
	position: relative;
	margin-bottom: 20px;
	margin-top: 10px;
}
.footer-area .ft-content .subcribe-form .add-btn {
	position: absolute;
	top: -2px;
	right: 0px;
	border: none;
	height: 50px;
}
.ft-content .subcribe-form .form-control{
	padding: 12px 13px;
	background: transparent;
	color: #fff;
}
.ft-content .subcribe-form .form-control::placeholder {
	color: #f8fbfd;
	opacity: 1;
	font-weight: bold;
}
.footer-area .ft-content h3{
	font-size: 23px;
	font-weight: 600;
	color: #fff;
	border-bottom: 1px solid gray;
	margin-bottom: 15px;
	padding-bottom: 8px;
	position: relative;
	left: 0;
	bottom: 0;
}
.footer-area .ft-content h3:before{
	position: absolute;
	content: '';
	width: 70px;
	height: 2px;
	left: 0;
	bottom: -2px;
	background: #757FB5;;
}
.footer-area .ft-content p{
	color: #fff;
	margin-top: 15px;
	margin-bottom: 30px;
}

.social-nav ul{
	display: flex;
	justify-content: flex-start;
}
.social-nav ul li{
	margin-right: 10px;
}
.social-nav ul li a{
	width: 30px;
	height: 30px;
	background:#163666;
	text-align: center;
	line-height: 31px;
	border-radius: 5px;
	color: #fff;
	display: inline-block;
}
.social-nav ul li a:hover{
	transform: translateY(-2px);
}
.social-nav ul li a i{
	font-size: 18px;
}
/*footer-list*/
.footer-list ul li{
	padding-bottom:7px;
	line-height: 1.8;
}
.footer-list ul li a {
	color: #f5faff;
	font-size: 17px;
	display: inline-block;
	font-family: 'Open Sans', sans-serif;
	font-weight: 400;
	transition: all .4s ease;
	position: relative;
}
.footer-list ul li a:hover {
	color: #B1C6E8;
	transform: translateX(15px);
}
.footer-list ul li a::after {
	content: '\f101';
	color: #fff;
	font-family:"Font Awesome 5 Free";
	font-weight: 700;
	display: block;
	font-size: 14px;
	position: absolute;
	top: 50%;
	left: 0;
	transform: translateY(-50%) translateX(-15px);
	opacity: 0;
	transition: all .4s ease;
}
.footer-list ul li a:hover::after {
	opacity: 1;
}
.foot-social ul li{
	padding-bottom:7px;
	line-height: 1.8;
}
.foot-social ul li a{
	color: #f5faff;
	font-size: 17px;
	display: inline-block;
	font-family: 'Open Sans', sans-serif;
	font-weight: 400;
	transition: all .3s ease;
}
.foot-social ul li a:hover{
	color: #B1C6E8;
}
.foot-social ul li a:hover i{
	color:#fff;
}
.foot-social ul li  i{
	width: 30px;
	height: 30px;
	background:#163666;
	text-align: center;
	line-height: 31px;
	border-radius: 5px;
	margin-right: 12px;
}

.btm-brd {
	margin-top: 75px;
	border-top: 1px solid #a4b9a2;
	padding-top: 20px;
}
.ft-left-btm p{
	color: #fff;
}
.ft-right-btm .ft-links ul{
	display: flex;
	justify-content: flex-end;
}
.ft-right-btm .ft-links ul li a {
	color: #fff;
	position: relative;
	padding: 0px 10px;
}
.border-btm::before {
	position: absolute;
	content: '';
	width: 2px;
	height: 12px;
	background: #a4b9a2;
	left: 0;
	top: 3px;
	
}

/*****************about-page********************/
.about-area {
	background: #ece4ff;
}
.page-area{
	padding-top: 200px;
	padding-bottom:150px;
	position: relative;
	background: #020940;
}
.page-content{
	text-align: center;
	color: #fff;
}
.page-content h2{
	font-weight: 600;
	font-family: 'Open Sans', sans-serif;
	padding-bottom: 10px;
}
.page-content ul{
	display: flex;
	justify-content: center;
} 
.page-content ul li a{
	padding: 10px 18px;
	position: relative;
	font-size: 17px;
	color: #fff;
}
.page-content ul li .active {
	color: #847ea2;
	font-weight: 500;
}
.page-content ul li .active::before {
	position: absolute;
	content: '';
	width: 10px;
	height: 10px;
	background: #847ea2;
	top: 15px;
	left: -5px;

}
.about-img{
	position: relative;
	z-index: 1;
}
.about-img img{
	width: 100%;
}
.about-img::before{
	position: absolute;
	content: '';
	height: 120px;
	width: 400px;
	top: -30px;
	left: -30px;
	z-index: -1;
	background: #57D7FA;
	border-radius: 5px;
}
.about-img::after {
	position: absolute;
	content: '';
	height: 120px;
	width: 400px;
	bottom: -35px;
	right: -39px;
	z-index: -1;
	background: #020940;
	border-radius: 5px;
}
/***********popup video*/

.choose-img img{
  width: 100%;
}

 .choose-img {
  position: relative;
}

 .choose-img .technology-video {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  -moz-box-pack: center;
  justify-content: center;
  -moz-box-align: center;
  align-items: center;
  display: -moz-box;
  display: flex;
}

.video-btn {
  display: inline-block;
  width: 70px;
  height: 70px;
  line-height: 70px;
  text-align: center;
  border-radius: 50%;
  color: #3EBDF2;
  position: relative;
  top: 3px;
  z-index: 1;
  background-color: #0F2653;
}
.video-btn::after, .video-btn::before {
  content: "";
  display: block;
  position: absolute;
  top: 0;
  right: 0;
  z-index: -1;
  bottom: 0;
  left: 0;
  border-radius: 50%;
  background-color: #0F2653;
}

.video-btn i {
  font-size: 20px;
  font-weight: 700;
  color: #fff;
  position: absolute;
  top: 0;
  left: 3px;
  -moz-box-pack: center;
  justify-content: center;
  -moz-box-align: center;
  align-items: center;
  display: -moz-box;
  display: flex;
  width: 100%;
  height: 100%;
}
.video-btn::before {
  -webkit-animation: ripple 1.6s ease-out infinite;
  animation: ripple 1.6s ease-out infinite;
}
.video-btn::after {
  -webkit-animation: ripple 1.6s ease-out infinite;
  animation: ripple 1.6s ease-out infinite;
}

@keyframes ripple {
0%, 35% {
    -webkit-transform: scale(0);
    transform: scale(0);
    opacity: 1;
}
50% {
    -webkit-transform: scale(1.5);
    transform: scale(1.5);
    opacity: .8;
}
100% {
    opacity: 0;
    -webkit-transform: scale(2);
    transform: scale(2);
}
}
.Testing-title{
	margin-top: 0px;
}

.page-nav-area {
	text-align: center;
	padding-top: 20px;
}
.page-nav-area .page-nav ul {
	display: flex;
	justify-content: center;
}
.page-nav-area .page-nav ul li{
	padding: 0 8px;
}
.page-nav-area .page-nav ul li a {
	width: 34px;
	height: 34px;
	background: #133C7B;
	border-radius: 50%;
	text-align: center;
	line-height: 35px;
	display: block;
	color: #fff;
}
.page-nav-area .page-nav ul li a:hover {
	background: #fff;
	color: #011668;
	border: 1px solid #071F57;
}
/*****************about-page********************/
/*****************solution-page********************/
.service-deteils-img img{
	width: 100%;
}
.service-deteils-content{
	margin-top: 30px;

}
.service-deteils-content-2{
	margin-top:0px;
}
.service-deteils-content-2 h2{
	color: #293A5C;
	padding-bottom: 10px;
}
.service-deteils-content h2{
	color: #293A5C;
	padding-bottom: 10px;
}
/*****************solution-page********************/
/*****************team-2********************/

.box{
     background-color: #774691;
     overflow: hidden;
     position: relative;
     margin: 10px 0px;
 }
.box::before {
	content: '';
	background: linear-gradient(to right bottom, #0D285E 50%, #02132F 51%);
	height: 100%;
	width: 100%;
	opacity: 0;
	transform: rotate(0) scale(2);
	position: absolute;
	top: 0;
	left: 0;
	z-index: 1;
	transition: all 0.5s ease 0s;
}
 .box:hover:before{
     opacity: 0.7;
     transform: rotate(360deg) scale(2);
 }
 .box img{
     width: 100%;
     height: auto;
     transition: all 0.5s ease 0s;
 }
 .box:hover img{
  filter: grayscale(50%);
   }
 .box .box-content{
     color: #fff;
     background: linear-gradient(to right, transparent, rgba(255, 255, 255, 0.5));
     text-align: right;
     width: 100%;
     padding: 10px;
     opacity: 0;
     position: absolute;
     bottom: 10px;
     left: 0;
     z-index: 2;
     transition: all 0.5s ease-out 0s;
 }
 .box:hover .box-content{
  opacity: 1; 
}
 .box .title{
     font-size: 22px;
     font-weight: 700;
     text-transform: uppercase;
     margin: 0 0 3px;
 }
 .box .post{
     color: #fff;
     font-size: 14px;
     font-weight: 500;
     letter-spacing: 2px;
     text-transform: capitalize;
     display: block;
 }
 .box .icon{
     padding: 0;
     margin: 0;
     list-style: none;
     opacity: 0;
     position: absolute;
     left: 10px;
     top: 12px;
     z-index: 2;
     transition: all 0.5s ease-out 0s;
 }
 .box:hover .icon{ opacity: 1; }
 .box .icon li{
     margin: 0 3px;
     display: inline-block;
 }
 .box .icon li a {
	color: #15054d;
	background-color: #fff;
	font-size: 15px;
	text-align: center;
	line-height: 36px;
	height: 35px;
	width: 35px;
	border-radius: 5px;
	display: block;
	transition: all 0.3s ease 0s;
}
 .box .icon li a:hover{
     color: #fff;
     background-color: #15054d;
     box-shadow: 0 0 10px rgba(255,255,255,0.5);
 }

/*pricing-page**************/
.pricing-single {
	background: white;
	box-shadow: 0px 0px 4px 2px #9b9696;
	padding: 30px;
	text-align: center;
	margin-bottom: 20px;
	position: relative;
	transition: all 0.3s ease;
	overflow: hidden;
	z-index: 1;
	border-radius: 5px;
}
.pricing-heading{
	margin-bottom: 15px;
	position: relative;
}
.pricing-heading h3{
	font-size: 26px;
	font-weight: 600;
	color: #293A5C;
}
.pricing-heading p{
	color: #293A5C;
	font-size: 18px;
	font-weight:bold;
	font-family:'Open sans-serif;';
}
.pricing-single span {
	font-size: 30px;
	color: #E8EBF0;
	width: 120px;
	height: 120px;
	font-weight: bold;
	border-radius: 50%;
	background: #04073B;
	display: block;
	margin: 0 auto 20px;
	line-height: 120px;
}
.pricing-single:hover {
	box-shadow: 0px 0px 4px 2px #828090;
	transform: translateY(-10px);
}
.pricing-single ul{
	text-align: center;
	margin: 0 auto 25px;
	max-width: 200px;
	text-align: left;
}
.pricing-single ul li{
	margin-bottom: 10px;
	padding-left: 25px;
	font-weight: 500;
	color: #293A5C;
	position: relative;
}
.pricing-single ul li i {
	font-size: 12px;
	display: inline-block;
	width: 20px;
	height: 20px;
	background-color: #daf6fd;
	line-height: 20px;
	border-radius: 50%;
	text-align: center;
	position: absolute;
	top: 5px;
	left: 0;
	color: #112c7d;
}
.popular {
	position: absolute;
	top: -15px;
	right: -50px;
	background: red;
	padding: 35px 40px 10px 35px;
	transform: rotate(45deg);
	color: #fff;
	background: #051760;
}
/*pricing-page**************/
/*testimonial**************/
.testimonial-area{
	background: #F5F5F5;
}
.testimonial-table {
	text-align: center;
	padding: 30px;
	background: white;
	box-sizing: border-box;
	box-shadow: 0 0 1px 3px #eeecec;
	position: relative;
	border-radius: 6px;
	transition: all .3s ease;
	margin-top: 80px;
}

.testimonial-table .test-content{
	margin-bottom: 60px;
	margin-top: 30px;
}
.testimonial-table .test-content i {
	font-size: 50px;
	color: #293A5C;
	position: absolute;
	top: -26px;
	margin: 0 auto;
	right: 0;
	left: 0;
}
.test-img img {
	border-radius: 50%;
	border: 6px solid #020940;
	position: absolute;
	bottom: -50px;
	margin: 0 auto;
	right: 0;
	left: 0;
}
.test-content h3{
	font-size: 26px;
	font-weight: 600;
	color: #293A5C;
	padding: 6px 0px;
}
.test-content span{
	color: #293A5C;
	font-size: 18px;
	font-weight:bold;
	font-family:'Open sans-serif';
}
.testimonial-table:hover{
	background: #020940;
	transition: all .3s ease;

}
.testimonial-table:hover img{
	border: 6px solid #fff;
}
/*testimonial**************/
/*login page***************/
.sign-from {
	padding: 30px;
	background: #fff;
	border-radius: 16px;
	box-shadow: 0 0 20px 3px #c1c1c1;
	max-width: 540px;
	margin: auto;
}

.sign-from .form-group {
	margin-bottom: 25px;
}
.sign-from .form-control {
	padding: 10px 20px;
}
.form-title {
	text-align: center;
	margin-bottom: 30px;
}
.form-title h3{
	font-size: 26px;
	font-weight: 600;
	color: #293A5C;
}
.form-title  p{
	color: #293A5C;
	font-size: 18px;
	font-weight:bold;
	font-family:'Open sans-serif';
}
.creat-text{
	text-align: center;
	padding-top: 12px;
}
.creat-text p{
	font-size: 15px;
	color:#293A5C; ;
}
.creat-text a{
	color: #3EBDF2;
	padding-left: 5px;
}
.form-check-label {
	color: #293A5C;
	font-size: 15px;
}
.agree-text{
	color: #3EBDF2;
	padding-left: 5px;
}
.agree-text:hover{
	color: #3EBDF2;
}
/*login page***************/
.contact-2::after {
	position: absolute;
	content: '';
	height: 120px;
	width: 400px;
	bottom: -32px;
	right: -16px;
	z-index: -1;
	background: #020940;
	border-radius: 5px;
}
.contact-single{
	background: #020940;
	
}
.contact-single .service-content h3{
	color: #fff;
}
.contact-single p{
	color: #fff;
}

/*homee222222*/
.silder-single{
	height: 600px!important;

}
.silder-single {
	width: 100%;
	background: #807e13;
	box-sizing: border-box;
	display: flex;
	justify-content: center;
	align-items: center;
	position: relative;
	
}
.slider-box{
	background-size: cover!important;
	background-position: 50%!important;
	background-repeat: no-repeat!important;
}
.silder-single::before {
	position: absolute;
	content: '';
	width: 100%;
	height: 100%;
	background: #030d1e;
	opacity: 0.5;

}
.slider-single-full h2 {
	font-size: 40px;
	color: #fff;
	font-weight: 900;
	margin-bottom: 15px;
	margin-top: 20px;
}
.slider-single-full p {
	font-size: 18px;
	color: #fff;

}
.slider-single-full{
	padding-top: 70px;
	padding-bottom: 100px;
}
.slider-single-full a {
	display: inline-block;
	padding: 20px 40px;
	background: #fff;
	color: #333;
	text-transform: uppercase;
	margin-top: 25px;
	text-decoration: none;
	-webkit-transition:all .3s ease-in-out;
	transition: all .3s ease-in-out;
	border-radius: 50px;
}

.nav-right .input-group-2 .form-control{
	background-color: #191b2821;
	opacity: 0.9;

}
.banner-slider .owl-dots {
	display: none;
}

.banner-slider .owl-nav .owl-prev {
	position: absolute;
	top: 50%;
	font-size: 17px !important;
	width: 45px;
	height: 45px;
	border-radius: 50%;
	border: none;
	line-height: 30px !important;
	background: #163666 !important;
	font-weight: 600;
	-webkit-transition: .3s;
	transition: .3s;
	color: #fff !important;
	-webkit-transform: translateY(-50px);
	transform: translateY(-50px);
	left: 1%;
	display: flex;
	justify-content: center;
	align-items: center;
}
.banner-slider .owl-nav .owl-next  {
	position: absolute;
	top: 50%;
	font-size: 17px !important;
	width: 45px;
	height: 45px;
	border-radius: 50%;
	border: none;
	line-height: 30px !important;
	background: #163666 !important;
	font-weight: 600;
	-webkit-transition: .3s;
	transition: .3s;
	color: #fff !important;
	-webkit-transform: translateY(-50px);
	transform: translateY(-50px);
	right: 1%;
	display: flex;
	justify-content: center;
	align-items: center;
	
}



/*Animation*/
.active .silder-single h2, .active .silder-single a, .active .silder-single p{
  -webkit-animation-fill-mode: both;
  animation-fill-mode: both;
  -webkit-animation-duration: 1s;
  animation-duration: 1s;
  -webkit-animation-name: fadeInLeft;
  animation-name: fadeInLeft;
  -webkit-animation-delay: 0.3s;
  animation-delay: 0.3s;
}
.active .silder-single p{
  -webkit-animation-delay: 0.5s;
  animation-delay: 0.5s;
}
.active .silder-single a {
  -webkit-animation-delay: 0.9s;
  animation-delay: 0.9s;
  -webkit-animation-name: fadeInUp;
  animation-name: fadeInUp;
}
.contact-img-2{
	animation: none;
}
.contact-img-2 img {
	border-radius: 12px;
	width: 100%;
	height: auto;
}



/* home2 tabs */

.ui-tabs .ui-tabs-nav {
	background: none;
	border: none;
}

.ui-tabs .ui-tabs-nav li.ui-tabs-active {
	margin-bottom: 1px;
	padding-bottom: 9px;
	background: none;
}

.ui-tabs .ui-tabs-nav li {
	list-style: none;
	float: left;
	position: relative;
	top: 0;
	border-bottom-width: 0;
	padding: 0px;
	white-space: nowrap;
	border: none;
}


.tabs-description p{
	color: #707070;
	font-size: 15px;
	font-weight: 400;
}
.ui-widget.ui-widget-content {
	border: none;
}
.ui-tabs .ui-tabs-nav li.ui-tabs-active .ui-tabs-anchor, .ui-tabs .ui-tabs-nav li.ui-state-disabled .ui-tabs-anchor, .ui-tabs .ui-tabs-nav li.ui-tabs-loading .ui-tabs-anchor {
	border:none;
	background: #08224A;
	color: #fff!important;
}


#tabs ul li a {
	color: #707070;
	font-size: 16px;
	font-weight: 400;
	border: 1px solid #6E7073;
	color: #08224A;
}
.tab-list ul li{
	color: #707070;
	font-size: 15px;
	font-weight: 400;
	line-height: 30px;
}
.tab-list ul li i{
	color:#717484;
	padding-right: 10px;
}


/******counter-area css********/

.counter-area {
	background: #F3F8FF;
	padding: 30px 0;
}

.single-counter {
	overflow: hidden;
	background-color: #0d0c48;
	text-align: center;
	border-radius: 5px;
	padding: 35px 15px 40px 15px;
	margin-left: 15px;
	transition: all .3s ease;
}
.single-counter:hover {
	background: linear-gradient(90deg, rgb(51, 67, 117) 0%, rgb(1, 10, 32) 100%);
	transition: all .3s ease;
}
.single-counter:hover .icon,
.single-counter:hover p,
.single-counter:hover h4{
	color: #fff;
}
.single-counter h4 {
	font-weight: bold;
	font-size: 52px;
	padding-bottom: 5px;
	color: #fff;
	font-family: 'Open Sans', sans-serif;
}
.single-counter p {
	color: #293A5C;
	text-transform: uppercase;
	font-size: 16px;
	font-weight: 700;
}
.single-counter .icon{
	font-size:40px;
	color: #3EBDF2;

}



/******counter-area css********/

/* appointment-text */
.inner-content {
	background: url('assets/img/home2/Untitled-1.jpg')no-repeat scroll 0 0 / 100% 100%;
	padding: 50px 53px;
	margin-top: -130px;
	z-index: 99;
	position: relative;
}
.inner-content::before{
	width: 100%;
	height: 100%;
	position: absolute;
	content: '';
	background: #0F2653;
	opacity: 0.8;
	z-index: -1;
	top: 0;
	left: 0;
}
.inner-content h3{
	color: #fff;

}
.inner-content p{
	color: #fff;

}
.appointment_form{
margin-top: 45px;
}
/* .appointment_form input {
	border: none;
	outline: none;
	width: 100%;
	display: block;
	background-color: #fff;
	height: 60px;
	padding-left: 20px;
	color: #fff;
	font-size: 18px;
	color: black;
} */
.appointment_form .form-control {

	padding: 10px.75rem;
	font-size: 15px;
	font-weight: 500;
	line-height: 1.5;
	color: #858C93;
	background-color: #fff;
	border: 1px solid #227edb;

}
.appointment_form select option{
	
}
.appointment-bottom-text{
	margin-top: 30px;
	display: flex;
	justify-content: space-between;
	text-align: center;

}
.appointment-bottom-text p {
	text-align: start;
	opacity: 0.7;
	width: 65%;
}
.rigth-btn {
	margin-right: 0;
}

/* blog_details */
.detail_image img{
	width: 100%;
	border-radius: 12px;
	height: auto;
}
.detail_content{
	margin-top: 20px;
}
.detail_content .content-date ul{
	display: flex;
}
.detail_content .content-date ul li{
	padding: 5px 12px;
	text-transform: capitalize;
	font-weight: 500;
	font-size: 16px;
	font-family: 'Open Sans', sans-serif;
}
.detail_content .content-date ul li a {
	font-size: 17px;
	color: #325599;
}

.detail_content h3 {
	margin-top: 20px;
	margin-bottom: 20px;
	font-size: 32px;
	color: #2b3e62;
}

.detail_content blockquote {
	background: #0F3269;
	padding: 45px;
	position: relative;
	z-index: 1;
	margin-top: 20px;
	margin-bottom: 20px;
	border-radius: 10px;
	font-style: italic;
}
.detail_content blockquote p{
	color: #fff;
}
.detail_content blockquote::before {
	position: absolute;
	content: "";
	top: 10px;
	left: 15px;
	height: 150px;
	width: 2px;
	background: #9290c6;
}
.detail_content blockquote::after {
	content: '';
	background-color: transparent;
	background-image: url('assets/img/blog/block-qoute-1-1.png');
	background-position: 0 0;
	background-repeat: no-repeat;
	width: 63px;
	height: 48px;
	opacity: 0.2;
	position: absolute;
	top: 27px;
	left: 83px;
}
.blg-dtl-img img{
	width: 100%;
	border-radius: 5px;
}
.page-nav2 ul {
	justify-content: start!important;
	display: flex;

}
.comment-content{
	margin-top: 40px;
}
.comment-content h3 {
	color: #0F2A5B;
	padding-bottom: 22px;
	font-weight: bold;
	font-size: 23px;
}

.comment-single {
	display: flex;
	margin-top: 42px;
	border-bottom: 1px solid #CFD0D2;
	padding-bottom: 15px;
	margin-bottom: 20px;
	border-radius: 6px;
}
.comment-single img {
	width: 70px;
	height: 70px;
	border-radius: 50%;
	border: 4px solid #0F2653;
}
.cmnt-text {
	margin-left: 19px;
}
.cmnt-text h4{
	color: #0F2653;
	text-transform: capitalize;
}
.cmnt-text span {
	color: #59657B;
	font-style: italic;
	text-transform: uppercase;
	font-size: 14px;
	padding: 3px 0px;
	display: inline-block;
}
.cmnt-text p {
	width: 90%;
	padding-bottom:10px;
}
.cmnt-text a {
	text-transform: capitalize;
	text-decoration: none;
	padding: 7px 13px;
	border: 1px solid white;
	display: inline-block;
	font-size: 12px;
	font-weight: bold;
	color: #eaeaec;
	background: #0F3269;
}
.cmnt-text a:hover{
	border: 1px solid #0F3269;
	color: #0F3269;
	background: #fff;
	transition: all 0.3s ease;
}
.comment-form .form-control{
	margin-bottom: 20px;
}
.leave-comment{
	margin-top: 20px;
}
.leave-comment h3{
	color: #0F2A5B;
	padding-bottom: 22px;
	font-weight: bold;
	font-size: 23px;
}

/* search-bar */
.search-bar h3{
	color: #0F2A5B;
	padding-bottom: 22px;
	font-weight: bold;
	font-size: 23px;
	position: relative;
	padding-left: 10px;
}
.search-bar h3::before {
	position: absolute;
	content: '';
	height: 29px;
	width: 2px;
	top: 0px;
	background: #0F2A5B;
	left: 0;
}
.search-bar form{
	position: relative;
}


.search-bar .nav-btn {
	position: static;
}
.search-bar .nav-btn .box-btn {
	top: 0;
	right: 0;
	position: absolute;
}

.search-bar .form-control {
	padding: 11px 13px;
}

.side-single-2{
	margin-top: 34px;
}
.item-single {
	display: flex;
	margin-top: 22px;
}
.item-single a img{
	width: 80px;
	height: 80px;
	border-radius: 5px;
}
.item-cnt{
	margin-left: 10px;
}
.item-cnt span{
	color: #59657B;
	font-style: italic;
	text-transform: uppercase;
	font-size: 14px;
	padding: 3px 0px;
	display: inline-block;
}
.item-cnt h5 a {
	color: #0F2653;
	text-transform: capitalize;
	font-size: 18px;
}
.item-cnt h5 a:hover {
	color: #49659B;
}
.post-warp{
	margin-top: 20px;
}

.post-warp ul{
	justify-content: start;
}
.post-warp ul li {
	padding-bottom: 10px;
	margin-bottom: 10px;
	border-bottom: 1px solid #d3d6dd;
	font-size: 15px;
	font-weight: 600;
	position: relative;
	padding-left: 18px;
}
.post-warp ul li::before {
	position: absolute;
	content: '';
	width: 10px;
	height: 10px;
	background: #3e4484;
	transform: rotate(319deg);
	top: 6px;
	left: 0;
}
.post-warp ul li a{
	display: block;
	color: #0F2A5B;
}
.post-warp ul li a span{
	float: right;
}
.sidebar__tagscloud a {
	display: inline-block;
	border: 1px solid #0F2A5B;
	color: #0F2A5B;
	padding: 6px 19px;
	font-weight: 600;
	font-size: 14px;
	margin-left: 11px;
	margin-top: 15px;
	-webkit-transition: all .3s ease;
	transition: all .3s ease;
}
.sidebar__tagscloud a:hover{
	background: #0F2A5B;
	color: #fff;
}

/* case-details */

.case-img img{
	width: 100%;
	border-radius: 12px;
	margin-bottom: 20px;
}
.cs-img img{
	width: 100%;
	height: auto;
}

.cs-content h3{
	color: #0F2A5B;
	padding-bottom:10px;
	font-weight: bold;
	font-size: 21px;
}